{"name": "fitfemme-app-native", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start --dev-client", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "eslint . --quiet"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.0.2", "@motify/core": "^0.18.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/slider": "4.5.6", "@react-native-masked-view/masked-view": "^0.3.2", "@react-native-picker/picker": "2.11.0", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^7.1.1", "@supabase/supabase-js": "^2.47.12", "@svgr/webpack": "^8.1.0", "@tradle/react-native-http": "^2.0.1", "@types/react-native-vector-icons": "^6.4.18", "base64-arraybuffer": "^1.0.2", "browserify-zlib": "^0.2.0", "crypto-browserify": "^3.12.1", "date-fns": "^4.1.0", "expo": "^53.0.9", "expo-av": "~15.1.4", "expo-blur": "~14.1.4", "expo-constants": "~17.1.6", "expo-dev-client": "^5.1.8", "expo-document-picker": "~13.1.5", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-router": "~5.0.7", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-updates": "~0.28.13", "expo-web-browser": "~14.1.6", "https-browserify": "^1.0.0", "i18n-js": "^4.5.1", "i18next": "^25.2.0", "lottie-react-native": "7.2.2", "moti": "^0.30.0", "path-browserify": "^1.0.1", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.4.0", "react-native": "0.79.2", "react-native-animatable": "^1.4.0", "react-native-calendars": "^1.1312.0", "react-native-chart-kit": "^6.12.0", "react-native-config": "^1.5.3", "react-native-country-flag": "^2.0.2", "react-native-date-picker": "^5.0.12", "react-native-feather": "^1.1.2", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-size-matters": "^0.4.2", "react-native-status-bar-height": "^2.6.0", "react-native-svg": "15.11.2", "react-native-svg-web": "^1.0.9", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "stream-browserify": "^3.0.0", "tslib": "^2.8.1", "victory": "^37.3.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@eslint/config-array": "^0.20.0", "@eslint/object-schema": "^2.1.6", "@react-native-community/cli": "18.0.0", "@react-native-community/cli-platform-android": "18.0.0", "@react-native-community/cli-platform-ios": "18.0.0", "@react-native/babel-preset": "0.79.2", "@react-native/eslint-config": "0.79.2", "@react-native/metro-config": "0.79.2", "@react-native/typescript-config": "0.79.2", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^19.0.0", "eslint": "^9.8.0", "glob": "^11.0.2", "husky": "^9.1.7", "jest": "^29.2.1", "jest-expo": "^53.0.5", "react-test-renderer": "19.0.0", "rimraf": "^6.0.1", "supabase": "^2.15.8", "typescript": "^5.3.3"}, "private": true, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["react-native-chart-kit", "react-native-status-bar-height"]}}}, "resolutions": {"react": "19.0.0", "react-dom": "19.0.0", "react-test-renderer": "19.0.0", "react-native-gesture-handler": "~2.24.0", "react-native-svg": "15.11.2", "@types/react": "~19.0.10", "eslint": "^9.8.0", "tslib": "^2.8.1"}, "overrides": {"glob": "^11.0.2", "rimraf": "^6.0.1", "inflight": "npm:lru-cache@^10.2.0", "@babel/plugin-proposal-class-properties": "npm:@babel/plugin-transform-class-properties@^7.27.1", "@babel/plugin-proposal-nullish-coalescing-operator": "npm:@babel/plugin-transform-nullish-coalescing-operator@^7.27.1", "@babel/plugin-proposal-optional-chaining": "npm:@babel/plugin-transform-optional-chaining@^7.27.1", "node-domexception": "npm:@ungap/structured-clone@^1.2.0", "domexception": "npm:@ungap/structured-clone@^1.2.0", "abab": "npm:base64-js@^1.5.1", "react-shallow-renderer": "^16.15.0", "@humanwhocodes/config-array": "npm:@eslint/config-array@^0.20.0", "@humanwhocodes/object-schema": "npm:@eslint/object-schema@^2.1.6", "tslib": "^2.8.1", "framer-motion": "^11.11.17", "ajv": "^8.17.1", "ajv-keywords": "^5.1.0"}}